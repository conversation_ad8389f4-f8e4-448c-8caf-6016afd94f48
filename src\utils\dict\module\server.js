/**
 * 陪诊服务记录相关常量定义
 */

// 服务类型
export const SERVICE_TYPE = {
  HALF_DAY: { value: 0, label: '半天', color: '#409EFF' },
  FULL_DAY: { value: 1, label: '全天', color: '#67C23A' }
}

// 服务状态
export const SERVICE_STATUS = {
  PENDING_ASSIGNMENT: { value: 0, label: '待分配', color: '#E6A23C' },
  ASSIGNED: { value: 1, label: '已分配', color: '#409EFF' },
  IN_PROGRESS: { value: 2, label: '进行中', color: '#409EFF' },
  COMPLETED: { value: 3, label: '已完成', color: '#67C23A' },
  CANCELLED: { value: 4, label: '已取消', color: '#909399' }
}

// 性别要求字典
export const GENDER_REQUIREMENT = {
  NO_REQUIREMENT: { value: 0, label: '无要求', color: '#909399' },
  MALE: { value: 1, label: '男性', color: '#409EFF' },
  FEMALE: { value: 2, label: '女性', color: '#F56C6C' }
}

// 确认状态字典
export const CONFIRM_STATUS = {
  PENDING: { value: 0, label: '待确认', color: '#E6A23C' },
  CONFIRMED: { value: 1, label: '已确认', color: '#67C23A' },
  REJECTED: { value: 2, label: '已拒绝', color: '#F56C6C' }
}

// 确认码验证规则
export const CONFIRM_CODE_PATTERN = /^\d{6}$/

// 图片相关限制
export const IMAGE_LIMITS = {
  MAX_COUNT: 9,                     // 最大图片数量
  MAX_SIZE: 10 * 1024 * 1024,      // 最大文件大小 10MB
  ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']  // 允许的文件类型
}

// 业务提示消息
export const BUSINESS_MESSAGES = {
  CONFIRM_SUCCESS: '确认成功',
  COMPLETE_SUCCESS: '服务完成',
  CANCEL_SUCCESS: '取消成功',
  UPLOAD_SUCCESS: '上传成功',
  DELETE_SUCCESS: '删除成功',
  INVALID_CONFIRM_CODE: '确认码不正确',
  SERVICE_NOT_FOUND: '服务记录不存在',
  OPERATION_NOT_ALLOWED: '当前状态不允许此操作'
}

export default {
  SERVICE_TYPE,
  SERVICE_STATUS,
  GENDER_REQUIREMENT,
  CONFIRM_STATUS,
  CONFIRM_CODE_PATTERN,
  IMAGE_LIMITS,
  BUSINESS_MESSAGES
}
