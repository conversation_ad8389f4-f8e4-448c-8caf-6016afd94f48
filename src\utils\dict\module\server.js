/**
 * 陪诊服务记录相关常量定义
 */

// 服务类型
export const SERVICE_TYPE = {
  HALF_DAY: 0,    // 半天
  FULL_DAY: 1     // 全天
}

export const SERVICE_TYPE_TEXT = {
  [SERVICE_TYPE.HALF_DAY]: '半天',
  [SERVICE_TYPE.FULL_DAY]: '全天'
}

// 服务状态
export const SERVICE_STATUS = {
  PENDING_ASSIGNMENT: 0,  // 待分配
  ASSIGNED: 1,           // 已分配
  IN_PROGRESS: 2,        // 进行中
  COMPLETED: 3,          // 已完成
  CANCELLED: 4           // 已取消
}

export const SERVICE_STATUS_TEXT = {
  [SERVICE_STATUS.PENDING_ASSIGNMENT]: '待分配',
  [SERVICE_STATUS.ASSIGNED]: '已分配',
  [SERVICE_STATUS.IN_PROGRESS]: '进行中',
  [SERVICE_STATUS.COMPLETED]: '已完成',
  [SERVICE_STATUS.CANCELLED]: '已取消'
}

// 服务状态颜色
export const SERVICE_STATUS_COLOR = {
  [SERVICE_STATUS.PENDING_ASSIGNMENT]: '#909399',  // 灰色
  [SERVICE_STATUS.ASSIGNED]: '#E6A23C',           // 橙色
  [SERVICE_STATUS.IN_PROGRESS]: '#409EFF',        // 蓝色
  [SERVICE_STATUS.COMPLETED]: '#67C23A',          // 绿色
  [SERVICE_STATUS.CANCELLED]: '#F56C6C'           // 红色
}

// 服务时间段
export const SERVICE_PERIOD = {
  MORNING: 0,    // 上午
  AFTERNOON: 1,  // 下午
  ALL_DAY: 2     // 全天
}

export const SERVICE_PERIOD_TEXT = {
  [SERVICE_PERIOD.MORNING]: '上午',
  [SERVICE_PERIOD.AFTERNOON]: '下午',
  [SERVICE_PERIOD.ALL_DAY]: '全天'
}

// 性别
export const GENDER = {
  MALE: 1,      // 男
  FEMALE: 2,    // 女
  OTHER: 3      // 其他
}

export const GENDER_TEXT = {
  [GENDER.MALE]: '男',
  [GENDER.FEMALE]: '女',
  [GENDER.OTHER]: '其他'
}

// 陪诊师性别要求
export const ACCOMPANY_GENDER_REQUIREMENT = {
  MALE: 1,        // 男
  FEMALE: 2,      // 女
  NO_LIMIT: 3     // 不限
}

export const ACCOMPANY_GENDER_REQUIREMENT_TEXT = {
  [ACCOMPANY_GENDER_REQUIREMENT.MALE]: '男',
  [ACCOMPANY_GENDER_REQUIREMENT.FEMALE]: '女',
  [ACCOMPANY_GENDER_REQUIREMENT.NO_LIMIT]: '不限'
}

// 取消人类型
export const CANCEL_USER_TYPE = {
  PATIENT: 'PATIENT',      // 患者
  ACCOMPANY: 'ACCOMPANY',  // 陪诊师
  PLATFORM: 'PLATFORM'    // 平台
}

export const CANCEL_USER_TYPE_TEXT = {
  [CANCEL_USER_TYPE.PATIENT]: '患者',
  [CANCEL_USER_TYPE.ACCOMPANY]: '陪诊师',
  [CANCEL_USER_TYPE.PLATFORM]: '平台'
}

// 图片状态
export const IMAGE_STATUS = {
  INVALID: 0,  // 无效
  VALID: 1     // 有效
}

export const IMAGE_STATUS_TEXT = {
  [IMAGE_STATUS.INVALID]: '无效',
  [IMAGE_STATUS.VALID]: '有效'
}

// 分配记录状态
export const ASSIGNMENT_STATUS = {
  INVALID: 0,  // 无效（已转派）
  VALID: 1     // 有效
}

export const ASSIGNMENT_STATUS_TEXT = {
  [ASSIGNMENT_STATUS.INVALID]: '无效（已转派）',
  [ASSIGNMENT_STATUS.VALID]: '有效'
}

// 是否转派
export const IS_TRANSFER = {
  NO: 0,   // 否
  YES: 1   // 是
}

export const IS_TRANSFER_TEXT = {
  [IS_TRANSFER.NO]: '否',
  [IS_TRANSFER.YES]: '是'
}

// 排序字段选项
export const ORDER_BY_OPTIONS = [
  { value: 'create_time', label: '创建时间' },
  { value: 'service_time', label: '服务时间' },
  { value: 'complete_time', label: '完成时间' },
  { value: 'update_time', label: '更新时间' }
]

// 排序方向选项
export const ORDER_DIRECTION_OPTIONS = [
  { value: 'desc', label: '降序' },
  { value: 'asc', label: '升序' }
]

// 确认码验证规则
export const CONFIRM_CODE_PATTERN = /^\d{6}$/


// 图片相关限制
export const IMAGE_LIMITS = {
  MAX_COUNT: 9,                     // 最大图片数量
  MAX_SIZE: 10 * 1024 * 1024,      // 最大文件大小 10MB
  ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']  // 允许的文件类型
}
// 业务提示消息
export const BUSINESS_MESSAGES = {
  CONFIRM_SUCCESS: '确认成功',
  COMPLETE_SUCCESS: '服务完成',
  CANCEL_SUCCESS: '取消成功',
  UPLOAD_SUCCESS: '上传成功',
  DELETE_SUCCESS: '删除成功',
  INVALID_CONFIRM_CODE: '确认码不正确',
  SERVICE_NOT_FOUND: '服务记录不存在',
  OPERATION_NOT_ALLOWED: '当前状态不允许此操作'
}

export default {
  SERVICE_TYPE,
  SERVICE_TYPE_TEXT,
  SERVICE_STATUS,
  SERVICE_STATUS_TEXT,
  SERVICE_STATUS_COLOR,
  SERVICE_PERIOD,
  SERVICE_PERIOD_TEXT,
  GENDER,
  GENDER_TEXT,
  ACCOMPANY_GENDER_REQUIREMENT,
  ACCOMPANY_GENDER_REQUIREMENT_TEXT,
  CANCEL_USER_TYPE,
  CANCEL_USER_TYPE_TEXT,
  IMAGE_STATUS,
  IMAGE_STATUS_TEXT,
  ASSIGNMENT_STATUS,
  ASSIGNMENT_STATUS_TEXT,
  IS_TRANSFER,
  IS_TRANSFER_TEXT,
  ORDER_BY_OPTIONS,
  ORDER_DIRECTION_OPTIONS,
  CONFIRM_CODE_PATTERN,
  IMAGE_LIMITS,
  BUSINESS_MESSAGES
}
