/**
 * 滚动加载工具
 * 用于控制滚动加载功能
 */

class ScrollLoadUtils {
  constructor() {
    this.logs = []
    this.isEnabled = true // 可以通过环境变量控制
  }
  /**
   * 检查滚动加载条件
   * @param {Object} conditions - 检查条件
   */
  checkLoadConditions(conditions) {
    const {
      loadMoreStatus,
      loading,
      hasMore,
    } = conditions

    const canLoad = loadMoreStatus === 'more' && !loading && hasMore


    return canLoad
  }
}

// 创建全局实例
const scrollLoadUtils = new ScrollLoadUtils()

// 导出实例和类
export { ScrollLoadUtils, scrollLoadUtils }
export default scrollLoadUtils
