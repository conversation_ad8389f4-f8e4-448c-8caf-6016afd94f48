import axios from 'axios'
import {UniAdapter} from 'uniapp-axios-adapter'
import { useUserStore } from '@/store/modules/user'
import { secondsToTimestamp } from '@/utils/utils'

const BASE_API = import.meta.env.VITE_API_ADDRESS

// 全局变量用于管理刷新状态
let isRefreshing = false
let failedQueue = []
let userStore = null // 用户store实例
let requestInstance = null // 保存axios实例的引用

// 处理队列中的请求
function processQueue(error) {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve()
    }
  })
  failedQueue = []
}

// 刷新token - 直接调用API避免循环依赖
async function refreshToken() {
  if (!userStore) {
    throw new Error('User store not initialized')
  }

  const refreshTokenValue = userStore.getRefreshToken()
  if (!refreshTokenValue) {
    throw new Error('No refresh token available')
  }

  // 检查 refreshToken 是否过期
  if (userStore.refreshTokenExpireTime?.value && userStore.refreshTokenExpireTime.value <= Date.now()) {
    throw new Error('Refresh token expired')
  }

  try {
    console.log('11111111',`1${BASE_API}/public/auth/token/refresh`);
    // 直接使用axios而不是request实例，避免循环依赖
    const [err,res] =  await uni.request({
      sync: true, // 使用同步请求
      url: `http://192.168.1.120:9091/public/auth/token/refresh`,
      method: 'POST',
      data: {
        refreshToken: refreshTokenValue
      },
      header: {
        'Content-Type': 'application/json'
      }
    }).then(response => {
      const result = response.data
      let data
      if (result.code === 200 || result.success) {
        data = result.data || result
      } else {
        throw new Error(result.message || '刷新token失败')
      }
  

      const accessToken = data.accessToken
      const refreshToken = data.refreshToken
      const accessTokenExpiresIn = data.accessTokenExpiresIn
      const refreshTokenExpiresIn = data.refreshTokenExpiresIn
      const accessExpireTime = secondsToTimestamp(accessTokenExpiresIn)
      const refreshExpireTime = secondsToTimestamp(refreshTokenExpiresIn)
      userStore.setTokens(accessToken, refreshToken, accessTokenExpiresIn, refreshTokenExpiresIn)
  
      return accessToken
    })
    

  } catch (error) {

    throw error
  }
}

// 处理认证错误
function handleAuthError() {
  if (userStore) {
    userStore.handleAuthError()
  }
}

class Request {
  constructor() {
    this.baseUrl = BASE_API
    this.setupAxios()
  }

  // 配置axios
  setupAxios() {
    // 配置适配器
    axios.defaults.adapter = UniAdapter

    // 创建axios实例
    this.instance = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // 保存实例引用到全局变量
    requestInstance = this.instance

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const accessToken = userStore ? userStore.getAccessToken() : ''
        if (accessToken && config.headers) {
          config.headers.Authorization = `Bearer ${accessToken}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      async(response) => {
                const originalRequest = response.config

        // 处理401错误（token过期）
        if (response?.status === 401 && !originalRequest._retry) {
          if (isRefreshing) {
            // 如果正在刷新token，将请求加入队列
            return new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject })
            }).then(() => {
              // 使用全局实例引用，避免this引用导致的循环调用
              return requestInstance(originalRequest)
            })
          }

          originalRequest._retry = true
          isRefreshing = true

          try {
            await refreshToken()
            processQueue(null)
            // 使用全局实例引用，避免this引用导致的循环调用
            return requestInstance(originalRequest)
          } catch (refreshError) {
            processQueue(refreshError)
            handleAuthError()
            return Promise.reject(refreshError)
          } finally {
            isRefreshing = false
          }
        }
        return response
      },
      async (error) => {
        return Promise.reject(error)
      }
    )
  }



  // 处理参数，移除空值
  handleParams(params) {
    if (!params) return {}
    const result = {}
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        result[key] = params[key]
      }
    })
    return result
  }

  // 获取token（保持向后兼容）
  getToken() {
    return userStore ? userStore.getAccessToken() : ''
  }

  // 处理URL
  handleUrl(path) {
    const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\\/?%&=]*)?/;
    const objExp = new RegExp(Expression);
    if (objExp.test(path)) {
      return path
    }
    return path.startsWith('/') ? path : `/${path}`
  }

  // 核心请求方法
  async request(options) {
    const {
      url,
      method = 'GET',
      data,
      params,
      headers = {},
      isNeedToken = true
    } = options
    
    try {
      // 处理请求参数
      const requestParams = this.handleParams(params)
      const requestData = this.handleParams(data)

      // 构建请求配置
      const config = {
        url: this.handleUrl(url),
        method: method.toLowerCase(),
        headers: { ...headers }
      }

      // 根据请求方法添加数据
      if (['get', 'delete'].includes(config.method)) {
        config.params = requestParams
      } else {
        config.data = requestData
      }

      // 如果不需要token，临时移除Authorization头
      if (!isNeedToken && config.headers) {
        delete config.headers.Authorization
      }
      console.log('config', config);
      console.log('this.instance', this.instance);
      
      const response = await this.instance(config)
      
      // 处理响应数据
      const result = response.data
      if (result.code === 200 || result.success) {
        return result.data || result
      } else {
        uni.hideLoading();
        // throw new Error(result.message || '请求失败')
      }
    } catch (error) {
      uni.hideLoading();
      // 网络错误处理
      if (!error.response) {
        uni.showToast({
          title: '请检查当前网络环境',
          icon: 'none'
        })
      }
      throw error
    }
  }



  // 对外暴露的方法
  get(url, params = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      params,
      ...options
    })
  }

  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }

}

// 初始化userStore
userStore = useUserStore()
const request = new Request()




// 导出请求实例
export default request
