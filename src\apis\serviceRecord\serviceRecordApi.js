import request from '@/utils/request/uni-request'
import dayjs from 'dayjs'
const base = '/accompany/service-record'

/**
 * 陪诊师端-陪诊服务记录相关API
 */
export const serviceRecordApi = {
  /**
   * 查询陪诊服务记录列表
   * @param {Object} queryParams - 查询参数
   * @param {number} queryParams.current - 当前页码，默认1
   * @param {number} queryParams.size - 每页显示条数，默认10
   * @param {string} queryParams.orderBy - 排序字段，多个字段用逗号分隔
   * @param {string} queryParams.orderDirection - 排序方向：asc-升序，desc-降序
   * @param {boolean} queryParams.searchCount - 是否查询总数，默认true
   * @param {string} queryParams.serviceNo - 服务单编号
   * @param {string} queryParams.orderNo - 订单编号
   * @param {number} queryParams.serviceType - 服务类型：0-半天，1-全天
   * @param {Array<number>} queryParams.serviceStatus - 服务状态数组：0-待分配，1-已分配，2-进行中，3-已完成，4-已取消
   * @param {string} queryParams.serviceTimeStart - 服务时间开始，格式："2025-07-23 00:00:00"
   * @param {string} queryParams.serviceTimeEnd - 服务时间结束，格式："2025-07-24 23:59:59"
   * @param {string} queryParams.confirmCode - 确认码
   * @returns {Promise} 服务记录列表响应
   */
  getServiceRecordList(queryParams = {}) {
    const defaultParams = {
      current: 1,
      size: 10,
      searchCount: true,
      orderBy: 'create_time',
      orderDirection: 'desc'
    }
    
    const params = { ...defaultParams, ...queryParams }
    
    return request.post(`${base}/my-services`, {
      ...params
    })
  },

  /**
   * 查询服务记录详情
   * @param {number} serviceRecordId - 服务记录ID
   * @returns {Promise} 服务记录详情响应
   */
  getServiceRecordDetail(serviceRecordId) {
    return request.get(`${base}/${serviceRecordId}`)
  },

  /**
   * 确认服务（确认预约码）
   * @param {Object} confirmData - 确认数据
   * @param {number} confirmData.serviceRecordId - 服务记录ID
   * @param {string} confirmData.confirmCode - 确认码，6位数字
   * @returns {Promise} 确认响应
   */
  confirmService(confirmData) {
    return request.post(`${base}/confirm`, confirmData)
  },

  /**
   * 完成服务
   * @param {Object} completeData - 完成服务数据
   * @param {number} completeData.serviceRecordId - 服务记录ID
   * @param {string} completeData.serverRecord - 服务记录，最大长度1000字符
   * @param {string} completeData.remark - 备注，最大长度500字符
   * @returns {Promise} 完成服务响应
   */
  completeService(completeData) {
    return request.post(`${base}/complete`, completeData)
  },

  /**
   * 获取今日服务记录
   * @returns {Promise} 今日服务记录响应
   */
  getTodayServiceRecords(params) {    
    return this.getServiceRecordList({
      serviceTimeStart: new dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      serviceTimeEnd: new dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      serviceStatus: [1, 2, 3],
      ...params
    })
  },
  /**
   * 获取遗留服务记录
   * @returns {Promise} 遗留服务记录响应
   */
  getOverdueServiceRecords(params) {    
    return this.getServiceRecordList({
      serviceTimeStart: '2000-01-01 00:00:00',
      serviceTimeEnd: new dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      serviceStatus: [2],
      ...params
    })
  },

  /**
   * 获取待确认的服务记录
   * @returns {Promise} 待确认服务记录响应
   */
  getPendingConfirmServices(params) {
    return this.getServiceRecordList({
      serviceStatus: [1], // 已分配状态
      orderBy: 'service_time',
      orderDirection: 'asc'
    })
  },

  /**
   * 获取进行中的服务记录
   * @returns {Promise} 进行中服务记录响应
   */
  getInProgressServices() {
    return this.getServiceRecordList({
      serviceStatus: [2], // 进行中状态
      orderBy: 'service_time',
      orderDirection: 'asc'
    })
  },

  /**
   * 获取已完成的服务记录
   * @param {Object} queryParams - 查询参数
   * @returns {Promise} 已完成服务记录响应
   */
  getCompletedServices(queryParams = {}) {
    return this.getServiceRecordList({
      ...queryParams,
      serviceStatus: [3], // 已完成状态
      orderBy: 'complete_time',
      orderDirection: 'desc'
    })
  },

  /**
   * 搜索服务记录
   * @param {Object} searchParams - 搜索参数
   * @param {string} searchParams.keyword - 关键词（患者姓名、医院名称等）
   * @param {Array<number>} searchParams.serviceStatus - 服务状态筛选
   * @param {string} searchParams.startDate - 开始日期
   * @param {string} searchParams.endDate - 结束日期
   * @returns {Promise} 搜索结果响应
   */
  searchServiceRecords(searchParams) {
    const queryParams = {}
    
    if (searchParams.keyword) {
      // 这里可以根据后端实际支持的搜索字段进行调整
      queryParams.patientName = searchParams.keyword
      queryParams.hospitalName = searchParams.keyword
    }
    
    if (searchParams.serviceStatus && searchParams.serviceStatus.length > 0) {
      queryParams.serviceStatus = searchParams.serviceStatus
    }
    
    if (searchParams.startDate) {
      queryParams.serviceTimeStart = searchParams.startDate + ' 00:00:00'
    }
    
    if (searchParams.endDate) {
      queryParams.serviceTimeEnd = searchParams.endDate + ' 23:59:59'
    }
    
    return this.getServiceRecordList(queryParams)
  }
}

export default serviceRecordApi
