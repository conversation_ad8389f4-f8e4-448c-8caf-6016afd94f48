# 服务端字典格式转换指南

## 概述

已成功将 `src/utils/dict/module/server.js` 中的字典格式转换为与 `dict.js` 一致的统一格式。

## 转换前后对比

### 转换前（旧格式）
```javascript
// 分离的值和文本定义
export const SERVICE_TYPE = {
  HALF_DAY: 0,
  FULL_DAY: 1
}

export const SERVICE_TYPE_TEXT = {
  [SERVICE_TYPE.HALF_DAY]: '半天',
  [SERVICE_TYPE.FULL_DAY]: '全天'
}
```

### 转换后（新格式）
```javascript
// 统一的字典格式
export const SERVICE_TYPE = {
  HALF_DAY: { value: 0, label: '半天', color: '#409EFF' },
  FULL_DAY: { value: 1, label: '全天', color: '#67C23A' }
}
```

## 新增字典

### SERVICE_TYPE (服务类型)
- `HALF_DAY`: { value: 0, label: '半天', color: '#409EFF' }
- `FULL_DAY`: { value: 1, label: '全天', color: '#67C23A' }

### SERVICE_STATUS (服务状态)
- `PENDING_ASSIGNMENT`: { value: 0, label: '待分配', color: '#E6A23C' }
- `ASSIGNED`: { value: 1, label: '已分配', color: '#409EFF' }
- `IN_PROGRESS`: { value: 2, label: '进行中', color: '#409EFF' }
- `COMPLETED`: { value: 3, label: '已完成', color: '#67C23A' }
- `CANCELLED`: { value: 4, label: '已取消', color: '#909399' }

### GENDER_REQUIREMENT (性别要求)
- `NO_REQUIREMENT`: { value: 0, label: '无要求', color: '#909399' }
- `MALE`: { value: 1, label: '男性', color: '#409EFF' }
- `FEMALE`: { value: 2, label: '女性', color: '#F56C6C' }

### CONFIRM_STATUS (确认状态)
- `PENDING`: { value: 0, label: '待确认', color: '#E6A23C' }
- `CONFIRMED`: { value: 1, label: '已确认', color: '#67C23A' }
- `REJECTED`: { value: 2, label: '已拒绝', color: '#F56C6C' }

## 使用示例

### 在 Vue 组件中使用

```vue
<template>
  <view>
    <!-- 显示服务类型 -->
    <text>{{ DictManager.getLabel('SERVICE_TYPE', serviceType) }}</text>
    
    <!-- 显示带颜色的服务状态 -->
    <view :style="{ color: DictManager.getColor('SERVICE_STATUS', status) }">
      {{ DictManager.getLabel('SERVICE_STATUS', status) }}
    </view>
    
    <!-- 服务类型选择器 -->
    <picker 
      :range="DictManager.toOptions('SERVICE_TYPE')" 
      range-key="label"
      @change="handleServiceTypeChange"
    >
      <view>选择服务类型</view>
    </picker>
  </view>
</template>

<script setup>
import DictManager from '@/utils/dict/dict'

const serviceType = ref(0) // 半天
const status = ref(2) // 进行中

// 首页实际使用的逻辑
const getServiceTitle = (serviceType) => {
  return serviceType === 0 ? '半天陪诊（四小时）' : '全天陪诊（八小时）'
}

const getServiceTimeText = (serviceTime, serviceType) => {
  return serviceTime + ' ' + DictManager.getLabel('SERVICE_TYPE', serviceType)
}
</script>
```

### 在首页中的实际应用

```vue
<!-- 首页订单列表中的使用 -->
<view class="order-title">
  {{ item.serviceType == 0 ? '半天陪诊（四小时）' : '全天陪诊（八小时）' }}
</view>

<text class="detail-text">
  {{ item.serviceTime + ' ' + DictManager.getLabel('SERVICE_TYPE', item.serviceType) }}
</text>

<view class="order-status">
  {{ DictManager.getLabel('ORDER_STATUS', item.serviceStatus || 0) }}
</view>
```

## 自动注册

所有服务端字典已自动注册到 `DictManager` 中：

```javascript
// dict.js 中的注册
static dictRegistry = {
  USER_STATUS,
  ORDER_STATUS,
  GENDER,
  ...serverDictList  // 包含所有服务端字典
}
```

## 优势

1. **统一格式**：所有字典使用相同的 `{ value, label, color }` 格式
2. **自动注册**：无需手动注册，自动包含在字典管理器中
3. **颜色支持**：每个字典项都有对应的颜色值
4. **类型安全**：统一的数据结构便于类型检查
5. **易于维护**：集中管理，便于修改和扩展

## 迁移说明

如果项目中有使用旧格式的代码，需要进行以下调整：

### 旧代码
```javascript
// 获取文本
const text = SERVICE_TYPE_TEXT[serviceType]

// 判断状态
if (status === SERVICE_STATUS.IN_PROGRESS) {
  // ...
}
```

### 新代码
```javascript
// 获取文本
const text = DictManager.getLabel('SERVICE_TYPE', serviceType)

// 判断状态（值保持不变）
if (status === 2) { // SERVICE_STATUS.IN_PROGRESS.value
  // ...
}
```

## 注意事项

1. **值保持不变**：所有字典项的 `value` 值与原来的常量值保持一致
2. **向后兼容**：原有的数值判断逻辑无需修改
3. **颜色统一**：使用项目统一的颜色规范
4. **扩展性**：可以轻松添加新的字典项和属性

## 测试验证

运行测试文件验证转换结果：

```javascript
import { testServerDict } from '@/utils/dict/test-server-dict.js'
testServerDict()
```

转换完成后，所有服务端字典都可以通过 `DictManager` 进行统一管理和使用。
