{
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页"
			}
		},
		{
			"path":"pages/userCenter",
			"style": {
				"navigationBarTitleText": "个人中心"
			}
		}
	],
	"subPackages": [
		{
      "root":"pages/login",
      "pages":[
        {
          "path": "login",
          "style": {
            "navigationBarTitleText": "登录"
          }
        },
        {
          "path": "resetPwd",
          "style": {
            "navigationBarTitleText": "重置密码"
          }
        }
      ]
    },
    {
      "root": "pages/user",
      "pages": [
        {
          "path": "personal",
          "style": {
            "navigationBarTitleText": "个人信息"
          }
        },

      ]
    },
    {
      "root":"pages/service",
      "pages": [
        {
          "path": "service-record",
          "style": {
            "navigationBarTitleText": "服务记录"
          }
        },
        {
          "path": "service-detail",
          "style": {
            "navigationBarTitleText": "服务详情"
          }
        }
      ]
    }
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"easycom": {
		"custom": {
		"^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
		"^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
		"^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue",
    "router-navigate": "uniapp-router-next/components/router-navigate/router-navigate.vue"
		}
	}
}
