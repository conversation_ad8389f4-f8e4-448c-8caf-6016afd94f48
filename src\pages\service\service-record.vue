<template>
  <view class="service-record-page">
    <view class="total-service">
      <view class="total-service-text">服务总量：96</view>
      <view class="total-service-tip">*仅统计已完成订单</view>
    </view>
        <!-- 订单列表 -->
    <view class="order-list">
      <uni-list>
        <uni-list-item
          v-for="(item, index) in orderList"
          :key="item.id || index"
          :show-arrow="false"
          :border="false"
          @click="handleOrderClick(item)"
        >
          <template v-slot:body>
            <view class="order-item">
              <view class="order-header">
                <view class="order-title">{{ item.serviceName || '半天陪诊（四小时）' }}</view>
                <view class="order-status">
                  {{ item.status }}
                </view>
              </view>
              <view class="br-view"></view>
              <view class="order-details">
                <view class="detail-row">
                  <view class="detail-icon">
                    <image class="icon-img" src="@/static/public/calendar.png" mode="aspectFit"></image>
                  </view>
                  <text class="detail-text">{{ item.serviceDate || '2025-06-10上午' }}</text>
                </view>
                <view class="detail-row">
                  <view class="detail-icon">
                    <image class="icon-img" src="@/static/public/location.png" mode="aspectFit"></image>
                  </view>
                  <text class="detail-text">{{ item.hospitalName || '云南省第一人民医院' }}</text>
                </view>
                <view class="detail-row">
                  <view class="detail-icon">
                    <image class="icon-img" src="@/static/public/location.png" mode="aspectFit"></image>
                  </view>
                  <text class="detail-text">{{ item.hospitalAreaName || '云南省第一人民医院(xx园区)' }}</text>
                </view>
                <view class="detail-row">
                  <view class="detail-icon">
                    <image class="icon-img" src="@/static/public/user.png" mode="aspectFit"></image>
                  </view>
                  <text class="detail-text">{{ item.patientInfo || '王建国 男 13812341234' }}</text>
                </view>
              </view>
            </view>
          </template>
        </uni-list-item>
      </uni-list>

      <!-- 加载更多组件 -->
      <uni-load-more
        :status="loadMoreStatus"
        :content-text="loadMoreText"
        @clickLoadMore="loadMoreData"
      ></uni-load-more>
    </view>
  </view>
</template>

<script setup>
import { onShow } from '@dcloudio/uni-app';
import serviceRecordApi from '@/apis/serviceRecord/serviceRecordApi';

const orderList = ref()

// 分页参数
const pagination = reactive({
  current: 1,        // 当前页码
  size: 10,      // 每页数量
  total: 0,          // 总数量
  totalPages: 0      // 总页数
})

// 加载状态
const loading = ref(false)
const refreshing = ref(false)

// 加载更多状态
const loadMoreStatus = ref('more') // more, loading, noMore
const loadMoreText = computed(() => {
  const textMap = {
    more: { contentdown: '上拉显示更多', contentrefresh: '正在加载...', contentnomore: '没有更多数据了' },
    loading: { contentdown: '上拉显示更多', contentrefresh: '正在加载...', contentnomore: '没有更多数据了' },
    noMore: { contentdown: '上拉显示更多', contentrefresh: '正在加载...', contentnomore: '没有更多数据了' }
  }
  return textMap[loadMoreStatus.value] || textMap.more
})

onShow(()=> {
  getOrderList()
})
const getOrderList = () => {
  serviceRecordApi.getTodayServiceRecords({...pagination}).then(res => {
    orderList.value = res.data
  })
}
// 加载更多数据
const loadMoreData = async () => {
  if (loadMoreStatus.value !== 'more' || loading.value) return

  loadMoreStatus.value = 'loading'
  pagination.current += 1

  await loadOrderList(false)
}
// 跳转详情
const handleOrderClick = (item) => {
  uni.navigateTo({
    url: `/pages/order/detail?id=${item.id}`
  })
}
</script>

<style lang="scss" scoped>
.service-record-page{
  padding: 22rpx;
  .total-service{
    display: flex;
    justify-content:space-between;
    align-items: center;
    .total-service-text{
      font-size: 32rpx;
      font-weight: 600;
      color: $uni-text-color-black;
    }
    .total-service-tip{
      font-size: 24;
      color: rgba(0,0,0,0.6);
    }
  }
  // 订单列表
.order-list {
  padding: 18rpx 22rpx;

  // uni-list 组件样式重置
  :deep(.uni-list) {
    background-color: transparent;
    border: none;
    // #ifdef MP-WEIXIN
    uni-list-item {
      background-color: transparent;
      border: none;
      padding: 0;
      margin-bottom: 12rpx;
      .uni-list-item{
        border-radius: 18rpx;
        
        .uni-list-item__container {
          border-radius: 18rpx;
          margin: 0;
          padding: 0;
          background-color: transparent;
          border: none;
        }
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
    /* #endif */
    // #ifndef mp-weixin
    .uni-list-item {
      background-color: transparent;
      border: none;
      padding: 0;
      margin-bottom: 12rpx;
      border-radius: 18rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .uni-list-item__container {
        border-radius: 18rpx;
        margin: 0;
        padding: 0;
        background-color: transparent;
        border: none;
      }
    }
    // #endif
  }
  .order-item {
    background-color: #fff;
    padding: 20rpx;
    width: 100%;
    .order-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12rpx;

      .order-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        flex: 1;
      }

      .order-status {
        padding: 8rpx 16rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.status-progress {
          background-color: #E3F9E9;
          color: #2BA471;
        }

        &.status-pending {
          background-color: #FFF0ED;
          color: #D54941;
        }

        &.status-completed {
          background-color: $uni-bg-color-grey;
          color: $uni-text-color-black;
        }

      }
    }
    .br-view{
      background-color: $uni-br-color;
      height: 1rpx;
    }
    .order-details {
      margin-top: 20rpx;
      display: flex;
      flex-direction: column;
      gap:18rpx;
      .detail-row {
        display: flex;
        align-items: center;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;

          .icon-img {
            width: 100%;
            height: 100%;
            opacity: 0.6;
          }
        }

        .detail-text {
          font-size: 28rpx;
          color: $uni-text-color-black;
          flex: 1;
        }
      }
    }
  }

  // uni-load-more 组件样式
  :deep(.uni-load-more) {
    margin-top: 32rpx;
    margin-bottom: 32rpx;

    .uni-load-more__text {
      font-size: 28rpx;
      color: #999;
    }

    .uni-load-more__img {
      width: 48rpx;
      height: 48rpx;
    }
  }
}
}
</style>
