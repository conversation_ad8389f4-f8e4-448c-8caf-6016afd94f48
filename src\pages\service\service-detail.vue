<template>
  <view class="service-detail-page">
    <!-- 状态栏 -->
    <view class="status-bar">
      <text class="status-text">订单进行中</text>
    </view>
    <!-- 分割线 -->
     <view class="br-view"></view>
    <!-- 服务标题 -->
    <view class="service-title">
      <text class="title-text">{{ serviceInfo.serviceName || '就医陪诊 | 半天陪诊（四小时）' }}</text>
    </view>

    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="card-color"></view>
        <view class="card-title">基本信息</view>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">服务地点:</text>
          <text class="info-value">{{ serviceInfo.serviceLocation || '云南省第一人民医院' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">服务对象:</text>
          <text class="info-value">{{ serviceInfo.patientName || '王建国' }} {{ serviceInfo.patientGender || '男' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系电话:</text>
          <text class="info-value">{{ serviceInfo.contactPhone || '13812341234' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">备用电话:</text>
          <text class="info-value">{{ serviceInfo.backupPhone || '13800006789' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">性别要求:</text>
          <text class="info-value">{{ serviceInfo.genderRequirement || '女性' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">订单编号:</text>
          <text class="info-value">{{ serviceInfo.orderNumber || '897850006761' }}</text>
        </view>
      </view>
    </view>

    <!-- 上传附件卡片 -->
    <view class="upload-card">
      <view class="card-header">
        <view class="card-color"></view>
        <view class="card-title">上传附件</view>
      </view>
      <view class="upload-grid">
        <view
          v-for="(item, index) in uploadImages"
          :key="index"
          class="upload-item"
          @click="handleImageClick(index)"
        >
          <image
            v-if="item.url"
            :src="item.url"
            class="upload-image"
            mode="aspectFill"
          />
          <view v-else class="upload-placeholder">
            <view class="upload-icon">+</view>
            <text class="upload-text">Replace with Image</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 服务记录卡片 -->
    <view class="record-card">
      <view class="card-header">
        <view class="card-color"></view>
        <view class="card-title">服务记录</view>  
        <view class="add-record-btn" @click="showAddRecordModal">
          <image
            class="image-view"
            src="@/static/order/file-add.png"
            mode="scaleToFill"
          />
          <text class="add-text">添加记录</text>
        </view>
      </view>
      6
      
      <view class="record-list">
        <view
          v-for="(record, index) in serviceRecords"
          :key="index"
          class="record-item"
        >
          <view class="record-time">{{ record.createTime || '2024-06-01 13:03' }}</view>
          <view class="record-content">{{ record.content || '此处显示服务者记录内容文案，同时给患者展示服务示及反馈显示以及反馈显示，此处显示服务记录内容文案，同时给结果显示以及反馈显示' }}</view>
        </view>
      </view>
    </view>

    <!-- 完成服务按钮 -->
    <view class="complete-btn-container">
      <button class="complete-btn" @click="completeService">完成服务</button>
    </view>

    <!-- 添加记录弹窗 -->
    <u-popup
      v-model="showAddRecord"
      mode="bottom"
      border-radius="20"
      :safe-area-inset-bottom="true"
    >
      <view class="add-record-popup">
        <view class="popup-header">
          <text class="popup-title">添加服务记录</text>
          <view class="close-btn" @click="showAddRecord = false">
            <text class="close-text">×</text>
          </view>
        </view>
        <view class="popup-content">
          <uni-easyinput
            v-model="newRecordContent"
            type="textarea"
            placeholder="请输入服务记录内容..."
            :auto-height="true"
            :maxlength="500"
          />
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @click="showAddRecord = false">取消</button>
          <button class="confirm-btn" @click="addServiceRecord">确认添加</button>
        </view>
      </view>
    </u-popup>

    <!-- 图片选择弹窗 -->
    <u-action-sheet
      v-model="showImagePicker"
      :list="imagePickerList"
      @click="handleImagePickerClick"
    />
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import serviceRecordApi from '@/apis/serviceRecord/serviceRecordApi'
import OssClient from '@/utils/oss'

const ossClient = new OssClient()

// 页面数据
const serviceInfo = ref({})
const serviceRecords = ref([
  {
    createTime: '2024-06-01 13:03',
    content: '此处显示服务者记录内容文案，同时给患者展示服务示及反馈显示以及反馈显示，此处显示服务记录内容文案，同时给结果显示以及反馈显示'
  },
  {
    createTime: '2024-06-01 13:03',
    content: '此处显示服务者记录内容文案，同时给患者展示服务示及反馈显示以及反馈显示，此处显示服务记录内容文案，同时给结果显示以及反馈显示'
  }
])

// 上传图片数据
const uploadImages = ref([
  { url: '' },
  { url: '' },
  { url: '' },
  { url: '' }
])

// 弹窗控制
const showAddRecord = ref(false)
const newRecordContent = ref('')
const showImagePicker = ref(false)
const currentImageIndex = ref(0)

// 图片选择器选项
const imagePickerList = ref([
  { text: '拍照', value: 'camera' },
  { text: '从相册选择', value: 'album' }
])

// 页面加载
onLoad((options) => {
  const { id } = options
  if (id) {
    loadServiceDetail(id)
  }
})

// 加载服务详情
const loadServiceDetail = async (id) => {
  try {
    const res = await serviceRecordApi.getServiceRecordDetail(id)
    serviceInfo.value = res
  } catch (error) {
    console.error('加载服务详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 显示添加记录弹窗
const showAddRecordModal = () => {
  showAddRecord.value = true
}

// 添加服务记录
const addServiceRecord = () => {
  if (!newRecordContent.value.trim()) {
    uni.showToast({
      title: '请输入记录内容',
      icon: 'none'
    })
    return
  }

  const newRecord = {
    createTime: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/\//g, '-'),
    content: newRecordContent.value
  }

  serviceRecords.value.unshift(newRecord)
  newRecordContent.value = ''
  showAddRecord.value = false

  uni.showToast({
    title: '添加成功',
    icon: 'success'
  })
}

// 处理图片点击
const handleImageClick = (index) => {
  currentImageIndex.value = index
  showImagePicker.value = true
}

// 处理图片选择器点击
const handleImagePickerClick = (item) => {
  showImagePicker.value = false

  const sourceType = item.value === 'camera' ? 'camera' : 'album'
  chooseImage(sourceType)
}

// 选择图片
const chooseImage = (sourceType) => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: [sourceType],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      const fileType = res.tempFiles[0].name.substring(res.tempFiles[0].name.lastIndexOf('.') + 1)
      uploadImage(tempFilePath, fileType)
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

// 上传图片
const uploadImage = async (filePath, fileType = 'jpg') => {
  uni.showLoading({
    title: '上传中...'
  })

  try {
    const url = await ossClient.upload(ossClient.randFileName(fileType), filePath)
    uploadImages.value[currentImageIndex.value].url = url

    uni.hideLoading()
    uni.showToast({
      title: '上传成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('上传失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  }
}

// 完成服务
const completeService = () => {
  uni.showModal({
    title: '确认完成',
    content: '确定要完成此次服务吗？',
    success: (res) => {
      if (res.confirm) {
        // TODO: 调用完成服务API
        uni.showToast({
          title: '服务已完成',
          icon: 'success'
        })

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.service-detail-page {
  min-height: 100vh;
  background-color: $uni-bg-color-grey;
  padding: 22rpx;
  padding-bottom: 120rpx;
  position: relative;
}

// 状态栏
.status-bar {
  background-color: #f0f0f0;
  padding: 32rpx 10rpx;
  text-align: left;
  .status-text {
    font-size: 40rpx;
    font-weight: 600;
    color: rgba(0,0,0,0.9);
  }
}
.br-view{
    background-color: $uni-br-color;
    height: 1rpx;
    width: 100vw;
    left: 0;
    position: absolute;
}
// 服务标题
.service-title {
  text-align: left;
  padding: 16rpx;
  padding-left: 10rpx;
  .title-text {
    font-size: 28rpx;
    color: rgba(0,0,0,0.9);
    font-weight: 600;
  }
}

// 卡片通用样式
.info-card,
.upload-card,
.record-card {
  background-color: #fff;
  margin: 20rpx 0rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: $uni-shadow-sm;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
    .card-color{
      width: 8rpx;
      height: 44rpx;
      border-radius: 18rpx;
      background-color: $uni-color-third-primary;
    }
    .card-title {
      line-height: 28rpx;
      margin-left: 20rpx;
      flex: 1;
      font-size: 28rpx;
      font-weight: 600;
      color: $uni-text-color-black;
    }
  }
}

// 基本信息
.info-list {
  .info-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 6rpx 0;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      font-size: 24rpx;
      color: rgba(0,0,0,0.6);
      flex-shrink: 0;
      width: 160rpx;
    }

    .info-value {
      font-size: 24rpx;
      color: rgba(0,0,0,0.9);
    }
  }
}

// 上传附件
.upload-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;

  .upload-item {
    aspect-ratio: 1;
    border-radius: 12rpx;
    overflow: hidden;
    background-color: #f5f5f5;

    .upload-image {
      width: 100%;
      height: 100%;
    }

    .upload-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .upload-icon {
        font-size: 48rpx;
        color: $uni-text-color-grey;
        margin-bottom: 12rpx;
      }

      .upload-text {
        font-size: 24rpx;
        color: $uni-text-color-grey;
        text-align: center;
      }
    }
  }
}

// 服务记录
.add-record-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: top;
  .image-view{
    width: 36rpx;
    height: 36rpx;
    margin-right: 8rpx;
  }
  .add-text {
    color: $uni-color-primary;
    font-size: 28rpx;
    font-weight: 600;
  }
}

.record-list {
  .record-item {
    padding: 24rpx 0;
    border-bottom: 1rpx solid $uni-border-1;

    &:last-child {
      border-bottom: none;
    }

    .record-time {
      font-size: 24rpx;
      color: $uni-text-color-grey;
      margin-bottom: 12rpx;
    }

    .record-content {
      font-size: 28rpx;
      color: $uni-text-color-black;
      line-height: 1.6;
    }
  }
}

// 完成服务按钮
.complete-btn-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: #fff;
  border-top: 1rpx solid $uni-border-1;

  .complete-btn {
    width: 100%;
    background-color: $uni-color-primary;
    color: #fff;
    font-size: 32rpx;
    font-weight: 600;
    border-radius: 44rpx;
    height: 88rpx;
    line-height: 88rpx;
    border: none;

    &:active {
      background-color: $uni-color-third-primary;
    }
  }
}

// 添加记录弹窗
.add-record-popup {
  padding: 32rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: $uni-text-color-black;
    }

    .close-btn {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .close-text {
        font-size: 40rpx;
        color: $uni-text-color-grey;
      }
    }
  }

  .popup-content {
    margin-bottom: 32rpx;

    :deep(.uni-easyinput__content-textarea) {
      min-height: 200rpx;
      border: 1rpx solid $uni-border-1;
      border-radius: 12rpx;
      padding: 20rpx;
    }
  }

  .popup-footer {
    display: flex;
    gap: 20rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      border: none;
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: $uni-text-color-black;
    }

    .confirm-btn {
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
}
</style>
