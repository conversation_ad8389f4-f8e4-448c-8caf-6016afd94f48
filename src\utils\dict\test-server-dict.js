/**
 * 测试服务端字典格式转换
 */

import DictManager from './dict.js'

// 测试函数
function testServerDict() {
  console.log('🧪 测试服务端字典格式转换...\n')

  // 测试 SERVICE_TYPE
  console.log('📝 测试 SERVICE_TYPE:')
  console.log('半天服务:', DictManager.getLabel('SERVICE_TYPE', 0)) // 应该输出: 半天
  console.log('全天服务:', DictManager.getLabel('SERVICE_TYPE', 1)) // 应该输出: 全天
  console.log('半天颜色:', DictManager.getColor('SERVICE_TYPE', 0)) // 应该输出: #409EFF
  console.log('全天颜色:', DictManager.getColor('SERVICE_TYPE', 1)) // 应该输出: #67C23A

  // 测试 SERVICE_STATUS
  console.log('\n📝 测试 SERVICE_STATUS:')
  console.log('待分配:', DictManager.getLabel('SERVICE_STATUS', 0)) // 应该输出: 待分配
  console.log('已分配:', DictManager.getLabel('SERVICE_STATUS', 1)) // 应该输出: 已分配
  console.log('进行中:', DictManager.getLabel('SERVICE_STATUS', 2)) // 应该输出: 进行中
  console.log('已完成:', DictManager.getLabel('SERVICE_STATUS', 3)) // 应该输出: 已完成
  console.log('已取消:', DictManager.getLabel('SERVICE_STATUS', 4)) // 应该输出: 已取消

  // 测试 GENDER_REQUIREMENT
  console.log('\n📝 测试 GENDER_REQUIREMENT:')
  console.log('无要求:', DictManager.getLabel('GENDER_REQUIREMENT', 0)) // 应该输出: 无要求
  console.log('男性:', DictManager.getLabel('GENDER_REQUIREMENT', 1)) // 应该输出: 男性
  console.log('女性:', DictManager.getLabel('GENDER_REQUIREMENT', 2)) // 应该输出: 女性

  // 测试 CONFIRM_STATUS
  console.log('\n📝 测试 CONFIRM_STATUS:')
  console.log('待确认:', DictManager.getLabel('CONFIRM_STATUS', 0)) // 应该输出: 待确认
  console.log('已确认:', DictManager.getLabel('CONFIRM_STATUS', 1)) // 应该输出: 已确认
  console.log('已拒绝:', DictManager.getLabel('CONFIRM_STATUS', 2)) // 应该输出: 已拒绝

  // 测试转换为选项
  console.log('\n📝 测试转换为选项:')
  const serviceTypeOptions = DictManager.toOptions('SERVICE_TYPE')
  console.log('服务类型选项:', serviceTypeOptions)

  const serviceStatusOptions = DictManager.toOptions('SERVICE_STATUS')
  console.log('服务状态选项:', serviceStatusOptions)

  // 测试获取所有字典名称
  console.log('\n📝 所有字典名称:')
  const dictNames = DictManager.getDictNames()
  console.log('字典列表:', dictNames)

  console.log('\n🎉 服务端字典测试完成!')
}

// 模拟首页使用场景
function simulateIndexPageUsage() {
  console.log('\n🏠 模拟首页使用场景...')

  // 模拟订单数据
  const mockOrderItem = {
    serviceTime: '2024-06-10上午',
    serviceType: 0, // 半天
    serviceStatus: 2 // 进行中
  }

  // 模拟首页显示逻辑
  const serviceTitle = mockOrderItem.serviceType === 0 ? '半天陪诊（四小时）' : '全天陪诊（八小时）'
  const serviceTimeText = mockOrderItem.serviceTime + ' ' + DictManager.getLabel('SERVICE_TYPE', mockOrderItem.serviceType)
  const statusText = DictManager.getLabel('ORDER_STATUS', mockOrderItem.serviceStatus)

  console.log('服务标题:', serviceTitle)
  console.log('服务时间:', serviceTimeText)
  console.log('服务状态:', statusText)

  console.log('\n✅ 首页使用场景测试完成!')
}

// 导出测试函数
export { testServerDict, simulateIndexPageUsage }

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testServerDict()
  simulateIndexPageUsage()
}
