/**
 * 字典管理模块
 * 提供统一的字典数据管理功能
 */
// 导入字典模块
// import * as dictModules from './module/*.js'

// 导出所有字典对象
export * as serverDictList from './module/server.js'
console.log("🚀 ~ serverDictList:", serverDictList)
// 用户状态字典
export const USER_STATUS = {
  ACTIVE: { value: 1, label: '正常', color: '#67C23A' },
  INACTIVE: { value: 0, label: '禁用', color: '#F56C6C' },
  PENDING: { value: 2, label: '待审核', color: '#E6A23C' }
}

// 订单状态字典
export const ORDER_STATUS = {
  IN_PROGRESS: { value: 2, label: '进行中', color: '#409EFF' },
  PENDING: { value: 1, label: '待开始', color: '#E6A23C' },
  COMPLETED: { value: 3, label: '已完成', color: '#67C23A' }
}



// 性别字典
export const GENDER = {
  MALE: { value: 1, label: '男' },
  FEMALE: { value: 2, label: '女' },
  UNKNOWN: { value: 0, label: '未知' }
}


// 字典工具类
class DictManager {
  // 字典注册表
  static dictRegistry = {
    USER_STATUS,
    ORDER_STATUS,
    GENDER,
    ...serverDictList
  }

  /**
   * 解析字典参数，支持传入字典对象或字符串引用
   * @param {Object|string} dictParam - 字典对象或字符串引用
   * @returns {Object} 字典对象
   */
  static resolveDict(dictParam) {
    if (typeof dictParam === 'string') {
      const dict = this.dictRegistry[dictParam]
      if (!dict) {
        console.warn(`字典 "${dictParam}" 未找到`)
        return {}
      }
      return dict
    }
    return dictParam || {}
  }

  /**
   * 根据值获取字典项
   * @param {Object|string} dict - 字典对象或字符串引用
   * @param {*} value - 要查找的值
   * @returns {Object|null} 字典项或null
   */
  static getByValue(dict, value) {
    const resolvedDict = this.resolveDict(dict)
    for (const key in resolvedDict) {
      if (resolvedDict[key].value === value) {
        return resolvedDict[key]
      }
    }
    return null
  }

  /**
   * 根据值获取标签
   * @param {Object|string} dict - 字典对象或字符串引用
   * @param {*} value - 要查找的值
   * @param {string} defaultLabel - 默认标签
   * @returns {string} 标签
   */
  static getLabel(dict, value, defaultLabel = '未知') {
    const item = this.getByValue(dict, value)
    return item ? item.label : defaultLabel
  }

  /**
   * 根据值获取颜色
   * @param {Object|string} dict - 字典对象或字符串引用
   * @param {*} value - 要查找的值
   * @param {string} defaultColor - 默认颜色
   * @returns {string} 颜色值
   */
  static getColor(dict, value, defaultColor = '#909399') {
    const item = this.getByValue(dict, value)
    return item && item.color ? item.color : defaultColor
  }

  /**
   * 将字典转换为数组格式
   * @param {Object|string} dict - 字典对象或字符串引用
   * @returns {Array} 字典数组
   */
  static toArray(dict) {
    const resolvedDict = this.resolveDict(dict)
    return Object.keys(resolvedDict).map(key => ({
      key,
      ...resolvedDict[key]
    }))
  }

  /**
   * 将字典转换为选项数组（用于下拉框等）
   * @param {Object|string} dict - 字典对象或字符串引用
   * @returns {Array} 选项数组
   */
  static toOptions(dict) {
    const resolvedDict = this.resolveDict(dict)
    return Object.keys(resolvedDict).map(key => ({
      value: resolvedDict[key].value,
      label: resolvedDict[key].label,
      ...resolvedDict[key]
    }))
  }

  /**
   * 根据code获取字典项
   * @param {Object|string} dict - 字典对象或字符串引用
   * @param {*} code - 要查找的code
   * @returns {Object|null} 字典项或null
   */
  static getByCode(dict, code) {
    const resolvedDict = this.resolveDict(dict)
    for (const key in resolvedDict) {
      if (resolvedDict[key].code === code) {
        return resolvedDict[key]
      }
    }
    return null
  }

  /**
   * 根据code获取标签
   * @param {Object|string} dict - 字典对象或字符串引用
   * @param {*} code - 要查找的code
   * @param {string} defaultLabel - 默认标签
   * @returns {string} 标签
   */
  static getLabelByCode(dict, code, defaultLabel = '未知') {
    const item = this.getByCode(dict, code)
    return item ? item.label : defaultLabel
  }

  /**
   * 注册新的字典
   * @param {string} name - 字典名称
   * @param {Object} dict - 字典对象
   */
  static registerDict(name, dict) {
    this.dictRegistry[name] = dict
  }

  /**
   * 获取所有已注册的字典名称
   * @returns {Array} 字典名称数组
   */
  static getDictNames() {
    return Object.keys(this.dictRegistry)
  }
}

// 导出字典管理器
export { DictManager }

// 导出默认实例
export default DictManager
