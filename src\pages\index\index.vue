<template>
  <view class="home-page">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <view class="user-avatar">
          <image class="avatar-img" src="../../static/logo.png" mode="aspectFill"></image>
        </view>
        <view class="user-details">
          <view class="user-name">王晓</view>
          <view class="user-job">工号：10086</view>
        </view>
        <view class="user-arrow" @click="goUserCenter">
          <image class="arrow-icon" src="@/static/public/chevron-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 标签导航 -->
    <view class="tab-navigation">
      <view
        v-for="(item, index) in tagList"
        :key="index"
        class="tab-item"
        :class="{ 'tab-active': item.current }"
        @click="handleTagClick(index)"
      >
        <text class="tab-title">{{ item.title }}</text>
        <view v-if="item.num > 0" class="tab-badge">{{ item.num }}</view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list">
      <uni-list>
        <uni-list-item
          v-for="(item, index) in orderList"
          :key="item.id || index"
          :show-arrow="false"
          :border="false"
          @click="handleOrderClick(item)"
        >
          <template v-slot:body>
            <view class="order-item">
              <view class="order-header">
                <view class="order-title">{{ item.serviceName || '半天陪诊（四小时）' }}</view>
                <view class="order-status" :class="getStatusClass(item.status)">
                  {{ getDictLabel('ORDER_STATUS', item.status || 0) }}
                </view>
              </view>
              <view class="br-view"></view>
              <view class="order-details">
                <view class="detail-row">
                  <view class="detail-icon">
                    <image class="icon-img" src="@/static/public/calendar.png" mode="aspectFit"></image>
                  </view>
                  <text class="detail-text">{{ item.serviceDate || '2025-06-10上午' }}</text>
                </view>
                <view class="detail-row">
                  <view class="detail-icon">
                    <image class="icon-img" src="@/static/public/location.png" mode="aspectFit"></image>
                  </view>
                  <text class="detail-text">{{ item.hospitalName || '云南省第一人民医院' }}</text>
                </view>
                <view class="detail-row">
                  <view class="detail-icon">
                    <image class="icon-img" src="@/static/public/location.png" mode="aspectFit"></image>
                  </view>
                  <text class="detail-text">{{ item.hospitalAreaName || '云南省第一人民医院(xx园区)' }}</text>
                </view>
                <view class="detail-row">
                  <view class="detail-icon">
                    <image class="icon-img" src="@/static/public/user.png" mode="aspectFit"></image>
                  </view>
                  <text class="detail-text">{{ item.patientInfo || '王建国 男 13812341234' }}</text>
                </view>
              </view>
            </view>
          </template>
        </uni-list-item>
      </uni-list>

      <!-- 加载更多组件 -->
      <uni-load-more
        :status="loadMoreStatus"
        :content-text="loadMoreText"
        @clickLoadMore="loadMoreData"
      ></uni-load-more>
    </view>
  </view>
</template>

<script setup>
import { useRouter } from "uniapp-router-next";
import DictManager from "@/utils/dict/dict"
import { ref, reactive, onMounted, computed } from "vue";
import serviceRecordApi from "@/apis/serviceRecord/serviceRecordApi";
import { onShow } from "@dcloudio/uni-app";
const router = useRouter();

// 标签导航数据
const tagList = ref([
  {
    title: '今日',
    num: 0,
    current: true,
    type: 'today'
  },
  {
    title: '遗留',
    num: 0,
    current: false,
    type: 'overdue'
  },
  {
    title: '待开始',
    num: 5,
    current: false,
    type: 'pending'
  },
  {
    title: '全部订单',
    num: 0,
    current: false,
    type: 'all'
  }
])

// 分页参数
const pagination = reactive({
  current: 1,        // 当前页码
  size: 10,      // 每页数量
  total: 0,          // 总数量
  totalPages: 0      // 总页数
})

// 订单列表数据
const orderList = ref([])

// 加载状态
const loading = ref(false)
const refreshing = ref(false)

// 加载更多状态
const loadMoreStatus = ref('more') // more, loading, noMore
const loadMoreText = computed(() => {
  const textMap = {
    more: { contentdown: '上拉显示更多', contentrefresh: '正在加载...', contentnomore: '没有更多数据了' },
    loading: { contentdown: '上拉显示更多', contentrefresh: '正在加载...', contentnomore: '没有更多数据了' },
    noMore: { contentdown: '上拉显示更多', contentrefresh: '正在加载...', contentnomore: '没有更多数据了' }
  }
  return textMap[loadMoreStatus.value] || textMap.more
})

// 当前选中的标签类型
const currentTabType = computed(() => {
  const activeTab = tagList.value.find(item => item.current)
  return activeTab ? activeTab.type : 'today'
})

// 页面挂载时初始化数据
onShow(() => {
  initData()
})

// 初始化数据
const initData = async () => {
  await loadOrderList(true)
  await loadTabCounts()
}
// 通过参数调用不同的api
const getServiceRecordListFun = (params) => {
  switch (currentTabType.value) {
    case 'today':
      return serviceRecordApi.getTodayServiceRecords(params)
    case 'overdue':
      return serviceRecordApi.getOverdueServiceRecords(params) // 添加一下方法
    case 'pending':
      return serviceRecordApi.getPendingConfirmServices(params)
    case 'all':
      return serviceRecordApi.getAllServiceRecords(params)
    default:
      return serviceRecordApi.getAllServiceRecords(params)
  }
}
// 加载订单列表
const loadOrderList = async (reset = false) => {
  if (loading.value) return

  try {
    loading.value = true

    if (reset) {
      pagination.current = 1
      orderList.value = []
      loadMoreStatus.value = 'more'
    }

    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      type: currentTabType.value
    }

    // TODO: 调用实际的API接口
    const response = await serviceRecordApi.getServiceRecordListFun(params)

    if (response && response.data) {
      const { list, total, totalPages } = response.data

      if (reset) {
        orderList.value = list || []
      } else {
        orderList.value.push(...(list || []))
      }

      pagination.total = total || 0
      pagination.totalPages = totalPages || 0

      // 更新加载更多状态
      if (pagination.current >= pagination.totalPages) {
        loadMoreStatus.value = 'noMore'
      } else {
        loadMoreStatus.value = 'more'
      }
    }
  } catch (error) {
    console.error('加载订单列表失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载标签统计数据
const loadTabCounts = async () => {
  try {
    // TODO: 调用实际的API接口获取各标签的数量
    const response = await fetchTabCountsAPI()

    if (response && response.data) {
      tagList.value.forEach(tab => {
        if (response.data[tab.type] !== undefined &&tab.title=='待开始') {
          tab.num = response.data[tab.type]
        }
      })
    }
  } catch (error) {
    console.error('加载标签统计失败:', error)
  }
}

// 标签点击事件
const handleTagClick = async (index) => {
  if (tagList.value[index].current) return

  tagList.value.forEach((item, i) => {
    item.current = i === index
  })

  // 切换标签时重新加载数据
  await loadOrderList(true)
}

// 订单点击事件
const handleOrderClick = (item) => {
  // TODO: 跳转到订单详情页
  console.log('点击订单:', item)
  uni.navigateTo({
    url: `/pages/order/detail?id=${item.id}`
  })
}

// 加载更多数据
const loadMoreData = async () => {
  if (loadMoreStatus.value !== 'more' || loading.value) return

  loadMoreStatus.value = 'loading'
  pagination.current += 1

  await loadOrderList(false)
}

// 下拉刷新
const onRefresh = async () => {
  if (refreshing.value) return

  refreshing.value = true
  await loadOrderList(true)
  await loadTabCounts()
  refreshing.value = false
}

// 获取字典标签
const getDictLabel = (dictName, value) => {
  const statusDict = {
    'in_progress': '进行中',
    'pending': '待开始',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusDict[value] || '未知状态'
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusClasses = {
    'in_progress': 'status-progress',
    'pending': 'status-pending',
    'completed': 'status-completed',
    'cancelled': 'status-cancelled'
  }
  return statusClasses[status] || 'status-default'
}


const fetchTabCountsAPI = async () => {
  // TODO: 替换为实际的API调用
  // return await orderApi.getTabCounts()

  // 模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          today: 3,
          overdue: 1,
          pending: 5,
          all: 20
        }
      })
    }, 500)
  })
}
// 跳转个人中心
const goUserCenter = () => {
    router.navigateTo({
      url: '/pages/userCenter'
    });
}
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 状态栏占位
.status-bar {
  height: var(--status-bar-height, 44px);
  background-color: #fff;
}


// 用户信息卡片
.user-card {
  padding: 32rpx;
  background-color: #fff;

  .user-info {
    display: flex;
    align-items: center;

    .user-avatar {
      width: 96rpx;
      height: 96rpx;
      margin-right: 24rpx;

      .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: #f0f0f0;
      }
    }

    .user-details {
      flex: 1;
      display: flex;
      align-items: center;
      .user-name {
        font-size: 32rpx;
        color: $uni-text-color-black;
      }

      .user-job {
        margin-left: 12rpx;
        background-color: $uni-bg-color-grey;
        font-size: 24rpx;
        color: $uni-text-color-black;
        line-height:40rpx;
        padding: 0 16rpx;
        font-weight: 400;
        border-radius: 200rpx;
        align-items: center;
        vertical-align:middle;
      }
    }

    .user-arrow {
      width: 48rpx;
      height: 48rpx;

      .arrow-icon {
        width: 100%;
        height: 100%;
        opacity: 0.6;
      }
    }
  }
}

// 标签导航
.tab-navigation {
  display: flex;
  background-color: #fff;
  padding: 8rpx 8rpx 16rpx 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  gap:16rpx;
  .tab-item {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 64rpx;
    border-radius: 1998rpx;
    transition: all 0.3s ease;
    background-color: $uni-bg-color-grey;

    .tab-title {
      font-size: 28rpx;
      color: #666;
      transition: color 0.3s ease;
    }

    .tab-badge {
      position: absolute;
      top: 2rpx;
      right: 16rpx;
      width: 32rpx;
      height: 32rpx;
      background-color: $uni-color-error;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20rpx;
      font-weight: 600;
      color: #fff;
      transform: scale(0.8);
    }

    &.tab-active {
      background-color: $uni-color-second-primary;

      .tab-title {
        color:$uni-color-primary;
        font-weight: 600;
      }
    }
  }
}

// 订单列表
.order-list {
  padding: 18rpx 22rpx;

  // uni-list 组件样式重置
  :deep(.uni-list) {
    background-color: transparent;
    border: none;
    // #ifdef MP-WEIXIN
    uni-list-item {
      background-color: transparent;
      border: none;
      padding: 0;
      margin-bottom: 12rpx;
      .uni-list-item{
        border-radius: 18rpx;
        
        .uni-list-item__container {
          border-radius: 18rpx;
          margin: 0;
          padding: 0;
          background-color: transparent;
          border: none;
        }
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
    /* #endif */
    // #ifndef mp-weixin
    .uni-list-item {
      background-color: transparent;
      border: none;
      padding: 0;
      margin-bottom: 12rpx;
      border-radius: 18rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .uni-list-item__container {
        border-radius: 18rpx;
        margin: 0;
        padding: 0;
        background-color: transparent;
        border: none;
      }
    }
    // #endif
  }
  .order-item {
    background-color: #fff;
    padding: 20rpx;
    width: 100%;
    .order-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12rpx;

      .order-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        flex: 1;
      }

      .order-status {
        padding: 8rpx 16rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.status-progress {
          background-color: #E3F9E9;
          color: #2BA471;
        }

        &.status-pending {
          background-color: #FFF0ED;
          color: #D54941;
        }

        &.status-completed {
          background-color: $uni-bg-color-grey;
          color: $uni-text-color-black;
        }

      }
    }
    .br-view{
      background-color: $uni-br-color;
      height: 1rpx;
    }
    .order-details {
      margin-top: 20rpx;
      display: flex;
      flex-direction: column;
      gap:18rpx;
      .detail-row {
        display: flex;
        align-items: center;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;

          .icon-img {
            width: 100%;
            height: 100%;
            opacity: 0.6;
          }
        }

        .detail-text {
          font-size: 28rpx;
          color: $uni-text-color-black;
          flex: 1;
        }
      }
    }
  }

  // uni-load-more 组件样式
  :deep(.uni-load-more) {
    margin-top: 32rpx;
    margin-bottom: 32rpx;

    .uni-load-more__text {
      font-size: 28rpx;
      color: #999;
    }

    .uni-load-more__img {
      width: 48rpx;
      height: 48rpx;
    }
  }
}

// 底部安全区域
.safe-area-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}
</style>
